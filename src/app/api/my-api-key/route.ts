import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, NotificationType } from "@prisma/client";
import prisma from "~/server/db/prisma";
import { generateUniqueApiKey } from "~/utils/apiAuth/apiKeyUtil";
import { createSystemNotificationForAdmins } from "~/utils/notifications/createSystemNotification";

/**
 * Get API key for the user's OU contact
 */
export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || !session?.user?.role || ![Role.CPO, Role.CARD_MANAGER].includes(session.user.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const userOuId = session.user.selectedOu?.id || session.user.ou?.id;
    
    if (!userOuId) {
      return NextResponse.json({ error: "Keine OU zugeordnet" }, { status: 400 });
    }

    // Find CPO contact for this OU
    const contact = await prisma.contact.findFirst({
      where: {
        ouId: userOuId,
        cpo: true,
      },
      select: {
        id: true,
        name: true,
        companyName: true,
        apiKey: true,
        ou: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    if (!contact) {
      return NextResponse.json({ 
        error: "Kein CPO-Contact für Ihre OU gefunden" 
      }, { status: 404 });
    }

    return NextResponse.json({ contact });
  } catch (error) {
    console.error("Error fetching API key:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * Generate or regenerate API key for the user's OU contact
 */
export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || !session?.user?.role || ![Role.CPO, Role.CARD_MANAGER].includes(session.user.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const userOuId = session.user.selectedOu?.id || session.user.ou?.id;
    
    if (!userOuId) {
      return NextResponse.json({ error: "Keine OU zugeordnet" }, { status: 400 });
    }

    // Find CPO contact for this OU
    const contact = await prisma.contact.findFirst({
      where: {
        ouId: userOuId,
        cpo: true,
      },
    });

    if (!contact) {
      return NextResponse.json({ 
        error: "Kein CPO-Contact für Ihre OU gefunden" 
      }, { status: 404 });
    }

    // Generate unique API key
    const apiKey = await generateUniqueApiKey();

    // Update contact with new API key
    const updatedContact = await prisma.contact.update({
      where: { id: contact.id },
      data: { apiKey },
      select: {
        id: true,
        name: true,
        companyName: true,
        apiKey: true,
        ou: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    // Create admin notification
    await createSystemNotificationForAdmins({
      nachricht: `🔑 API Key ${contact.apiKey ? 'regeneriert' : 'generiert'} für CPO-Contact "${contact.name || contact.companyName || 'Unbekannt'}" durch ${session.user.role} "${session.user.name} ${session.user.lastName}" (OU: ${session.user.selectedOu?.name || session.user.ou?.name})`,
      type: NotificationType.INFO,
    });

    return NextResponse.json({
      message: contact.apiKey ? "API key regenerated successfully" : "API key generated successfully",
      contact: updatedContact,
    });
  } catch (error) {
    console.error("Error generating API key:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
