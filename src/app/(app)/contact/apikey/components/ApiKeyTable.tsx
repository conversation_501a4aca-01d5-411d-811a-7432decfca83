"use client";

import React, { useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import Table from "~/utils/table/table";
import { GridOptions, ICellRendererParams } from "ag-grid-community";
import { ColDef } from "ag-grid-community/dist/lib/entities/colDef";
import Button from "~/component/button";
import { AiOutlineKey, AiOutlineDelete, AiOutlineCopy } from "react-icons/ai";

interface Contact {
  id: string;
  name: string | null;
  companyName: string | null;
  apiKey: string | null;
  cpo: boolean;
  ou: {
    id: string;
    name: string;
    code: string;
  } | null;
}

interface ApiKeyTableProps {
  contacts: Contact[];
}

export const ApiKeyTable: React.FC<ApiKeyTableProps> = ({ contacts }) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});

  const setLoading = (contactId: string, loading: boolean) => {
    setLoadingStates(prev => ({ ...prev, [contactId]: loading }));
  };

  const generateApiKey = async (contactId: string) => {
    setLoading(contactId, true);
    try {
      const response = await fetch("/api/contact/apikey", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ contactId }),
      });

      if (response.ok) {
        startTransition(() => {
          router.refresh();
        });
      } else {
        const error = await response.json();
        alert(`Fehler beim Generieren des API Keys: ${error.error}`);
      }
    } catch (error) {
      alert("Fehler beim Generieren des API Keys");
      console.error("Error generating API key:", error);
    } finally {
      setLoading(contactId, false);
    }
  };

  const deleteApiKey = async (contactId: string) => {
    if (!confirm("Sind Sie sicher, dass Sie den API Key löschen möchten?")) {
      return;
    }

    setLoading(contactId, true);
    try {
      const response = await fetch("/api/contact/apikey", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ contactId }),
      });

      if (response.ok) {
        startTransition(() => {
          router.refresh();
        });
      } else {
        const error = await response.json();
        alert(`Fehler beim Löschen des API Keys: ${error.error}`);
      }
    } catch (error) {
      alert("Fehler beim Löschen des API Keys");
      console.error("Error deleting API key:", error);
    } finally {
      setLoading(contactId, false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert("API Key in die Zwischenablage kopiert!");
    } catch (error) {
      console.error("Error copying to clipboard:", error);
      alert("Fehler beim Kopieren in die Zwischenablage");
    }
  };

  const ActionsCellRenderer = (params: ICellRendererParams) => {
    const contact = params.data as Contact;
    const isLoading = loadingStates[contact.id] || false;

    return (
      <div className="flex gap-2 items-center">
        {contact.apiKey ? (
          <>
            <Button
              type="button"
              onClick={() => copyToClipboard(contact.apiKey!)}
              className="text-xs px-2 py-1 bg-blue-500 hover:bg-blue-600"
              disabled={isLoading}
            >
              <AiOutlineCopy className="mr-1" size="0.8rem" />
              Kopieren
            </Button>
            <Button
              type="button"
              onClick={() => deleteApiKey(contact.id)}
              className="text-xs px-2 py-1 bg-red-500 hover:bg-red-600"
              disabled={isLoading}
            >
              <AiOutlineDelete className="mr-1" size="0.8rem" />
              {isLoading ? "..." : "Löschen"}
            </Button>
          </>
        ) : (
          <Button
            type="button"
            onClick={() => generateApiKey(contact.id)}
            className="text-xs px-2 py-1 bg-green-500 hover:bg-green-600"
            disabled={isLoading}
          >
            <AiOutlineKey className="mr-1" size="0.8rem" />
            {isLoading ? "Generiere..." : "Generieren"}
          </Button>
        )}
      </div>
    );
  };

  const ApiKeyRenderer = (params: ICellRendererParams) => {
    const apiKey = params.value as string | null;
    
    if (!apiKey) {
      return <span className="text-gray-400 italic">Kein API Key</span>;
    }

    // Show only first 8 and last 4 characters for security
    const maskedKey = `${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}`;
    
    return (
      <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
        {maskedKey}
      </span>
    );
  };

  const StatusRenderer = (params: ICellRendererParams) => {
    const hasApiKey = !!params.data.apiKey;
    
    return (
      <span className={`px-2 py-1 rounded text-xs font-semibold ${
        hasApiKey 
          ? "bg-green-100 text-green-800" 
          : "bg-gray-100 text-gray-800"
      }`}>
        {hasApiKey ? "Aktiv" : "Inaktiv"}
      </span>
    );
  };

  const columnDefs: ColDef[] = [
    {
      field: "name",
      headerName: "Name",
      pinned: "left",
      width: 200,
    },
    {
      field: "companyName",
      headerName: "Firma",
      width: 200,
    },
    {
      field: "cpo",
      headerName: "Typ",
      width: 100,
      valueFormatter: (params) => params.value ? "CPO" : "EMP",
    },
    {
      field: "ou.name",
      headerName: "OU",
      width: 150,
      valueGetter: (params) => params.data.ou?.name || "Keine OU",
    },
    {
      field: "apiKey",
      headerName: "API Key",
      width: 200,
      cellRenderer: ApiKeyRenderer,
    },
    {
      field: "status",
      headerName: "Status",
      width: 100,
      cellRenderer: StatusRenderer,
    },
    {
      field: "actions",
      headerName: "Aktionen",
      pinned: "right",
      width: 200,
      cellRenderer: ActionsCellRenderer,
      sortable: false,
      filter: false,
    },
  ];

  const gridOptions: GridOptions = {
    rowModelType: "clientSide",
    defaultColDef: {
      sortable: true,
      filter: true,
      resizable: true,
      filterParams: {
        buttons: ["reset", "apply"],
      },
    },
    rowHeight: 50,
  };

  return (
    <div className="w-full">
      <div className="h-[600px] w-full">
        <Table
          rowData={contacts}
          columnDefs={columnDefs}
          gridOptions={gridOptions}
        />
      </div>
    </div>
  );
};
